
input FeishuBitableSyncConfig {
    name: String
}

input FeishuApp {
    id: Int
    appName: String
    appToken: String
}

type FeishuAppVO {
    id: Int
    appName: String
    appToken: String
}

type FeishuBitableSyncConfigVO{
    id: Int
    name: String
    appId: Int
    tableId: String
    tableName: String
    dataApiUrl: String
    primaryKeyList: [String]
    fieldsConfigList: Object
    cronExpression: String
    active: Boolean
    createdAt: DateTime
    updatedAt: DateTime
}

type FeishuBitableSyncConfigResult {
    list: [FeishuBitableSyncConfigVO]
    pagination: Pagination
}