type Query {
    ########## 域名解析 相关接口(Query)  ##########

    #获取域名列表
    getDomains(requestVo:DescribeDomainsRequest,pageVo:DOSPageVo):DescribeDomainsResponse
    #获取域名信息
    getDomainInfo(requestVo:DescribeDomainInfoRequest):DescribeDomainInfoResponse
    #获取域名操作日志
    getDomainOperateLogs(requestVo:DescribeDomainLogsRequest,pageVo:DOSPageVo):DescribeDomainLogsResponse
    #获取指定域名的所有的解析记录
    getDomainRecords(request:DescribeDomainRecordsRequest,pageVo:DOSPageVo):DescribeDomainRecordsResponse
    #获取指定解析记录的信息
    getDomainRecordInfo(request:DescribeDomainRecordInfoRequest):DescribeDomainRecordInfoResponse
    #*****************************************************************************************************#

    ########## 阿里云日志 相关接口(Query)  ##########

    # 查询阿里云的日志url - v2.2.3
    getLoginUrl(releaseId:Int!):String
    # 重定向查询阿里云的日志url - v2.2.3
    redirectUrl(releaseId:Int!):String
    # 查询yaml文件历史记录 -v2.2.5
    getYamlHistory(kubernetesResourceId:Int):[DOSKubernetesYamlRelation]
    #*****************************************************************************************************#

    ########## 日志查询 相关接口(Query)  ##########

    # 查询k8s列表 - v2.2.3  必传参数：projectId，envTypeId
    getKubernetesResources(kubernetesResource:DOSKubernetesResourceVo):[DOSKubernetesResourcePo]
    # 分页查询k8s列表 - V2.2.4
    getPageList(kubernetesResource:DOSKubernetesResourceVo,pageVo:DOSPageVo):DOSKubernetesResourcePoResult
    #*****************************************************************************************************#

    ########## 集群查询 相关接口(Query)  ###########

    # 根据集群id获取集群 - new
    getClusterById(clusterId:Int):DOSCluster
    # 获取集群 - 2.2.0
    getClusters(cluster:DOSClusterPage,pageVo:DOSPageVo):DOSClusterResult
    # 获取全部集群 - 2.2.0
    getAllClusters(cluster:DOSClusterPage):[DOSClusterPo]
    # 根据集群id查询集群命名空间 - new
    getClusterNamespaces(namespace:DOSClusterNamespacePage,pageVo:DOSPageVo):DOSClusterNamespaceResult
    # 根据名称获取命名空间 - new
    queryClusterNamespaceByName(namespace:DOSClusterNamespaceVo,pageVo:DOSPageVo):DOSClusterNamespaceResult
    # 根据集群id获取全部的命名空间
    getNamespaces(clusterId:Int!):[DOSClusterNamespace]
    # 根据人员角色信息查询集群和命名空间级联 - v2.2.2
    getClusterNamespace:[DOSClusterNamespacePo]
    # 根据projectId和envType获取集群命名空间二级级联信息 - 2.2.0
    getClustersByProjectIdAndEnvType(projectId:Int!,envType:Int):[DOSClusterNamespacePo]
    # 获取env cluster namespace 三级级联信息 - 2.2.0
    getEnvClusterNamespaceCascade:[DOSEnvClusterPo]
    #*****************************************************************************************************#

    ########## 人员查询 相关接口(Query)  ##########

    # 查询所有人员 - new
    getAllUsers:[BaseUser]
    # 查询人员 - new
    getBaseUser:BaseUser
    # 分页查询人员 - v2.2.10
    getPageUsers(dosUserVM:DOSUserVM,inputPage:DOSPageVo):BaseUserTableResult
    # 根据name模糊查询人员 - new
    getUsersByName(name:String):[BaseUser]
    # 生成用户密码
    generateUserPassword:String
    #*****************************************************************************************************#

    ########## 项目-项目列表 相关接口(Query)  ##########

    # 查询项目列表 - V2.2.10
    getProjectList(projectVo:DOSProjectVo,pageVo:DOSPageVo):DOSProjectPoResult
    # 获取项目开发人员 - v2.2.10
    getProjectDevelopers(projectId:Int):[DOSProjectUserRelationPo]
    # 获取项目测试人员 - v2.2.10
    getProjectQas(projectId:Int):[DOSProjectUserRelationPo]
    # 查询项目组和环境的列表 - new
    getEnvAndGroup:DOSEnvAndGroup
    # 查询module - new
    getModuleByList(projectId:Int):[DOSProjectModulePo]
    # 查询项目相信信息 - 2.2.0
    getProjectInfo(projectId:Int!):DOSProjectInfoPo
    # 查询已绑定和未绑定资源的项目 - new
    getBindProjects(resourceId:Int!):[DOSBindStatusProject]
    #*****************************************************************************************************#

    ########## 项目组 相关接口(Query)  ##########

    # 分页查询项目组 - new
    getProjectGroupList(modelPage:DOSModelVo,pageVo:DOSPageVo):DOSProjectGroupResult
    # 根据项目组名称模糊查询 - new
    getProjectGroupByName(projectGroup:DOSProjectGroupVo,pageVo:DOSPageVo):DOSProjectGroupResult
    # 查询项全部项目组 - new
    getAllProjectGroupList:[DOSProjectGroup]
    # 根据人员角色信息查询项目组和项目的级联 - v2.2.11
    getCascade:[DOSProjectGroupPo]
    # 查询项目成员 - v2.2.10
    getProjectGroupMember(projectGroupId:Int):[DOSProjectGroupUserRelationPo]
    # 查询项目组绑定的技术经理 - v2.2.10
    getProjectGroupManager(projectGroupId:Int):[DOSProjectGroupUserRelationPo]
    # 查询项目组所有人员 - v2.2.10
    getProjectGroupUsers(projectGroupId:Int):[DOSProjectGroupUserRelationPo]
    # 获取待绑定的技术经理列表 - v2.2.10
    getTechnologyManager:[BaseUser]
    #*****************************************************************************************************#

    ########## chart列表 相关接口(Query)  ##########
    # 查询chart列表
    getChartList(paramVo:DOSChartParamVo,pageVo:DOSPageVo):DOSChartParamPoResult
    # 查询chart绑定的人员列表
    getChartUserList(chartId:Int):[DOSChartUserVo]
    # 查询部署的chart列表 - new
    getCharts(projectId:Int):[DOSChartPo]
    # 查询环境仓库级连 - new
    getEnvRepositories:[DOSHelmChartRepository]
    # 查询helm的api地址 - new
    getChartUrl:String
    # 查询chart的模板
    getChartTemplates(chartId:Int!,clusterId:Int):[DOSChartTemplate]
    #*****************************************************************************************************#

    ########## chart仓库列表 相关接口(Query)  ##########

    # 多条件查询仓库 - new
    getRepositorys(repository:DOSHelmChartRepositoryParam,pageVo:DOSPageVo):DOSHelmChartRepositoryResult
    #*****************************************************************************************************#

    ########## 菜单 相关接口(Query)  ##########

    # 根据用户登录信息获取menu列表 - new
    getMenus:[DOSMenuPo]
    # 新菜单查询 -V2.2.5
    getDOSMenus:[DOSMenu]
    # 查询角色绑定和未绑定的菜单 - v2.2.10
    getBindMenus(roleId:Int,isDel:Int):[DOSRoleMenuRelationPo]
    # 获取未添加的菜单 - v2.2.10
    getNoAddMenus:[DOSMenu]
    #*****************************************************************************************************#

    ########## 指标 相关接口(Query)  ##########

    # 根据登录人员身份信息分页获取指标信息
    getPageMetrics(dosMetricsVo:DOSMetricsVo,pageVo:DOSPageVo):DOSMetricsPoResult
    # 获取指标相关的所有数据库信息 -V2.2.8
    getAllMetricsDbs:[DOSMetricsDb]
    #*****************************************************************************************************#

    ########## k8s release 相关接口(Query)  ##########

    #根据releaseId查询k8s集群内对应的release详情 -V2.2.16
    getK8sReleaseInfo(releaseId:Int):DOSK8sRelease
    # 根据projectId查询k8s集群内对应的项目所有release详情
    getK8sProjectReleaseInfo(releaseVo:DOSReleaseVo):DOSK8sRelease
    #查询release resource详情
    getK8sResourceInfo(release:DOSK8sReleaseVo):DOSK8sRelease
    # 查询pod详情 - new
    getPodInfo(podVo:DOSPodVo):DOSPodInfo
    #*****************************************************************************************************#

    ########## 项目版本查询 相关接口(Query)  ##########

    # 获取项目版本操作字典 - 2.2.0
    getOperateAction:[DOSProjectVersionOperateAction]
    # 分页查询版本信息 - 2.2.0
    getVersionPage(operation:DOSVersionOperationVo,pageVo:DOSPageVo):DOSProjectVersionResult
    # 分页查询待审核的项目版本列表 - 2.2.0
    getToBeReviewed(operation:DOSVersionOperationVo,pageVo:DOSPageVo):DOSProjectVersionResult
    # 查询版本详细信息 - 2.2.0
    getVersionInfo(operation:DOSVersionOperationVo):DOSProjectVersionPhaseListPo
    # 查询项目可以选择的环境类型 - 2.2.0
    getSelectEnvType(projectId:Int):[DOSProjectEnvTypePo]
    # 查询版本操作日志 - 2.2.0
    getOperationLog(operationLog:DOSVersionOperationLogVo):[DOSVersionOperationLogPo]
    #*****************************************************************************************************#

    ########## release查询 相关接口(Query)  ##########

    # helm查看release的历史 - V2.2.2 必传的参数 releaseId
    historyRelease(param: DOSReleaseParamVo): DOSHelmOperationPo
    # 查询release列表 - V2.2.3 releaseType为必填项
    getReleaseList(releaseVo:DOSReleaseVo,pageVo:DOSPageVo):DOSReleasePoResult
    # 查询values - v2.2.2 必传参数：releaseType=2时releaseId必传；releaseType=1时revision、release、namespace、releaseId必传
    getReleaseValues(valuesVo:DOSReleaseValuesVo):DOSValuePo
    # 根据项目id和环境类型id查询release列表 - 2.2.2 必填参数：envType、projectId、releaseType
    getReleasesByProjectEnvType(releaseVo:DOSReleaseVo):[DOSReleasePo]
    # 查询release的image和tag
    getImageTag(valuesVo:DOSReleaseValuesVo):String
    #*****************************************************************************************************#

    ########## 资源查询查询 相关接口(Query)  ##########

    # 分页查询资源 - new
    getResources(resource:DOSResourceVo,pageVo:DOSPageVo):DOSResourceResult
    # 查询已绑定和未绑定项目的资源 - new
    getBindResource(projectId:Int!,type:Int):[DOSBindResourcePo]
    # 查询资源类型 - new
    getAllResourceType:[DOSResourceType]
    # 查询redis列表 - V2.2.12
    getRedisList:[DOSResources]
    # 查询数据库数据源的连接信息 - v2.2.19
    getDatabases(type:Int):[DOSResources]
    #*****************************************************************************************************#
    
    ########## awr报告查询 相关接口(Query)  ##########

    # 分页查询报告
    getAwrReportPage(pageVo:DOSPageVo, timeRangePo:TimeRangePo):DOSOracleAwrReportVo
    # 查询最近几条报告
    getAwrReport(size:Int!):[DOSOracleAwrReport]
    #*****************************************************************************************************#

    ########## 用户登录 相关接口(Query)  ##########

    # 返回登录界面 - new
    loginPage:String
    # 用户登录 - new
    login(loginUser:LoginUserVM):BaseUserLoginVm
    # 退出登录 - new
    loginOut:LoginOutVm
    # 通过code登录
    loginByCode(code:String!):BaseUserLoginVm
    #*****************************************************************************************************#

    ########## 角色查询 相关接口(Query)  ##########

    # 获取角色列表 - new
    getRoles:[DOSRole]
    # 查询所有的角色信息 - v2.2.10 参数：roleName（非必填）
    getAllRoles(roleVo:DOSRoleVo):[DOSRolePo]
    #*****************************************************************************************************#

    ########## ingress查询 相关接口(Query)  ##########
    # 根据登录的人员查询ingress
    getIngressByUser(ingressVo:DOSK8sIngressVo,pageVo:DOSPageVo):DOSK8sIngressResultPo
    # 获取集群下所有的ingress - v2.2.6
    getIngress(clusterVo:DOSClusterVo,pageVo:DOSPageVo):DOSK8sIngressResultPo
    #*****************************************************************************************************#

    ########## 项目与webHook关系查询 ##########
    # 跟项目id查询webHook与项目关系列表
    getWebhooks(projectId:Int):[DOSProjectWebhookPo]
    # 分页查询webHook
    getWebhookPage(webhook:DOSProjectWebhook,pageVo:DOSPageVo):DOSProjectWebhookPoResult
    # 测试webhook是否能够连通
    testWebhook(webhook:DOSProjectWebhook):String
    # 测试webhook的配置信息
    testWebhookCustomInfo(webhookInfoVo:DOSWebhookInfoVo):String
    #*****************************************************************************************************#

    ########## 查询版本开始结束的release状态 ##########
    # 查询版本创建到结束的release的变化
    getReleaseRecords(releaseRecordVo:DOSProjectVersionReleaseRecordVo):[DOSProjectVersionReleaseRecordListPo]

    ########## 查询kafka topic相关接口 ##########
    # 查询kafka topic列表
    getKafkaTopicList(kafkaTopicPo:KafkaTopicPo,pageVo:DOSPageVo):KafkaTopicVoResult
    # 根据项目id查询kafka topic列表
    getKafkaTopicsByProject(projectId:Int):[KafkaTopicVo]
    # 根据topic id查询topic详情
    getKafkaTopicById(topicId:Int):KafkaTopicVo
    # 根据topic id查询topic的绑定项目列表
    getProjectsBindStatusByKafkaTopicId(topicId:Int):[DOSBindStatusProject]

    ########## 查询export data job相关接口 ##########
    # 查询job详情
    getExportDataJobById(jobId:Int!):DOSExportDataJobPo
    # 查询job列表
    getExportDataJobList(exportDataJob:DOSExportDataJob, pageVo:DOSPageVo):DOSExportDataJobPoResult
    # 查询jobBatch列表
    getExportDataJobBatchList(exportDataJobBatch:DOSExportDataJobBatch, pageVo:DOSPageVo):DOSExportDataJobBatchPoResult
    # 查询job执行记录
    getExportDataJobLogList(exportDataJobLogVo:DOSExportDataJobLogVo, pageVo:DOSPageVo):DOSExportDataJobLogPoResult
    # 根据type查询所有的job
    getAllExportDataJobs(type:Int!):[DOSExportDataJobPo]
    
    ########## 查询tools相关接口 ##########
    # 查询工具列表
    getTools(tool:Tools, pageVo:DOSPageVo):ToolPageResult
    # 查询工具分类列表
    getToolCategories(pageVo:DOSPageVo):ToolCategoryPageResult
    
    ########## 查询飞书同步配置相关接口 ##########
    # 查询飞书同步配置列表
    getFeishuBitableSyncConfigList(feishuBitableSyncConfig:FeishuBitableSyncConfig, pageVo:DOSPageVo):FeishuBitableSyncConfigResult
    # 查询飞书同步配置
    getFeishuBitableSyncConfigById(configId:Int!):FeishuBitableSyncConfigVO
    # 查询飞书app列表
    getFeishuAppList:[FeishuAppVO]
}
