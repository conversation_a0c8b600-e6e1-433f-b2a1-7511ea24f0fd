type Mutation {

    ########## 角色操作 相关接口(Mutation)  ##########
    # 为角色分配菜单 - v2.2.10 参数：id（必填），menuIds(必填)
    bindMenus(roleVo:DOSRoleVo):Message
    # 删除角色信息 - v2.2.10
    deleteRole(roleId:Int):Boolean
    # 更新角色信息 - v2.2.10 参数 id（必填），roleName（非必填），roleLevel（非必填），remark（非必填）
    updateRole(dosRole:DOSRoleVo):DOSRole
    # 添加角色 - v2.2.10 参数：roleName（必填），roleLevel（必填），remark（非必填）
    saveRole(dosRole:DOSRoleVo):DOSRole
    #*****************************************************************************************************#

    ########## release操作 相关接口(Mutation)  ##########

    # helm部署新release - V2.2.2  必传的参数 projectId,clusterId,namespaceId,chartId,releases,envType;可选参数:values
    deployRelease(param: DOSReleaseParamVo): DOSHelmOperationPo
    # helm升级release - V2.2.2 必传的参数 releaseId 可选参数 values
    upgradeRelease(param: DOSReleaseParamVo): DOSHelmOperationPo
    # helm回滚release - V2.2.2 必传的参数 releaseId,revision
    rollbackRelease(param: DOSReleaseParamVo): DOSHelmOperationPo
    # helm卸载release - V2.2.2 必传的参数 releaseId
    uninstallRelease(param: DOSReleaseParamVo): DOSHelmOperationPo
    # 根据releaseId重启release - 2.2.0
    restartRelease(releaseId:Int!):Boolean
    #*****************************************************************************************************#

    ########## 域名解析 相关接口(Mutation)  ##########

    #给指定域名添加解析继续
    addDomainRecord(request:AddDomainRecordRequest):AddDomainRecordResponse
    #删除指定的解析记录
    deleteDomainRecord(request:DeleteDomainRecordRequest):DeleteDomainRecordResponse
    #修改指定的解析记录
    updateDomainRecord(request:UpdateDomainRecordRequest):UpdateDomainRecordResponse
    #*****************************************************************************************************#

    ########## kubectl 命令操作 相关接口(Mutation)  ##########

    # yaml文件卸载项目 - v2.2.3
    removeProject(kubernetesResourceId:Int!):Message
    # yaml文件升级项目 - v2.2.3 必传参数：kubernetesResourceId、yamlValues
    upgradeProject(kubernetesResource:DOSKubernetesResourceVo):Message
    # yaml文件部署项目 - v2.2.3 必传的参数：projectId、envTypeId、clusterId、yamlValues、namespaceId
    applyProject(kubernetesResource:DOSKubernetesResourceVo):Message
    #*****************************************************************************************************#

    ########## 集群操作 相关接口(Mutation)  ##########

    # 添加集群 - new
    saveCLuster(cluster:DOSClusterVo):DOSClusterPo
    #更新集群信息 - new
    updateCluster(cluster:DOSClusterVo):Boolean
    # 通过集群的id删除集群 - new
    deleteCluster(clusterId:Int!):Boolean
    # 添加集群命名空间 - new
    addClusterNamespace(namespace:DOSClusterNamespaceVo):DOSClusterNamespace
    # 更新集群命名空间 - new
    updateClusterNamespace(namespace:DOSClusterNamespaceVo):Boolean
    #*****************************************************************************************************#

    ########## 集群远端操作 相关接口(Mutation)  ##########

    #删除ingress - v2.2.6 必传参数：id、namespaceId、ingressName
    deleteIngress(clusterVo:DOSClusterVo):Message
    #*****************************************************************************************************#

    ########## 项目管理-项目组 相关接口(Mutation)  ##########

    # 为项目组分配人员 - v2.2.10
    distributionProjectGroupUsers(relation:DOSProjectGroupUserRelationVo):Boolean
    # 添加项目组 -V2.2.10
    saveProjectGroup(projectGroup:DOSProjectGroupVo):DOSProjectGroup
    # 根据id更新项目组 - new
    updateProjectGroup(projectGroup:DOSProjectGroupVo):Boolean
    # 删除项目组 - new
    deleteProjectGroup(projectGroupId:Int!):Boolean
    #*****************************************************************************************************#

    ########## 项目-项目列表 相关接口(Mutation)  ##########

    # 添加信项目 - V2.2.10
    saveProject(project:DOSProjectParam):Boolean
    # 更新项目信息 - V2.2.10
    updateProjects(project:DOSUpdateProjectVo):Boolean
    # 删除项目 - V2.2.10
    deleteProject(projectId:Int!):Boolean
    # 对项目进行授权 -V2.2.10
    authorizeProject(authorizes:[DOSProjectEnvClusterNamespaceRelation]):Boolean
    # 给项目分配开发人员 - V2.2.10
    distributionProjectDevelopers(relation:[DOSProjectUserRelationVo],projectId:Int):Boolean
    # 给项目分配测试人员- V2.2.10
    distributionProjectQas(relation:[DOSProjectUserRelationVo],projectId:Int):Boolean
    # 删除项目分配的人员 -V2.2.10
    deleteProjectUserRelation(relationId:Int):Int
    # 添加module  -V2.2.10
    saveProjectModule(module:DOSProjectModule):DOSProjectModulePo
    # 更新module  -V2.2.10
    updateModule(module:DOSProjectModuleUpdates):Boolean
    # 删除module  -V2.2.10
    deleteModule(moduleId:Int):Boolean
    #*****************************************************************************************************#

    ########## k8s release 相关接口(Mutation)  ##########

    # 对deployment扩容 - new
    scaleDeployment(deployment:DOSK8sDeploymentVo):Boolean
    # 重启deployment - new
    restartDeployment(deployment:DOSK8sDeploymentVo):Boolean
    # 删除deployment - new
    deleteDeployment(deployment:DOSK8sDeploymentVo):Boolean
    # 删除pod - new
    deletePod(podVo:DOSPodVo):Boolean
    #*****************************************************************************************************#

    ########## chart 相关接口(Mutation)  ##########

    # 添加chart - new
    saveHelm(chart:DOSHelmChartVo):DOSChartParamPo
    # 更新chart - new
    updateChart(chart:DOSHelmChartUpdateVo):Boolean
    # 删除chart - new
    deleteChart(chartId:Int):Boolean
    # 为chart分配人员 - new
    distributionChartUser(relation:DOSHelmChartUserRelationVo):Boolean
    #*****************************************************************************************************#

    ########## chart仓库 相关接口(Mutation)  ##########

    # 添加仓库 - new
    addRepository(repository:DOSHelmChartRepositoryVo):DOSHelmChartRepository
    # 根据仓库id更新仓库信息 - new
    updateRepository(repository:DOSHelmChartRepositoryParam):Boolean
    # 根据id删除仓库 - new
    deleteRepository(repositoryId:Int!):Boolean
    #*****************************************************************************************************#

    ########## 菜单 相关接口(Mutation)  ##########

    # 删除菜单 - v2.2.10
    deleteMenu(menuId:Int):Message
    # 更新菜单 - v2.2.10 参数：type 1：拖动，2：删除、id(必填)、name（必填）、isDel(必填)、parentMenuId（必填）、orderNum（非必填）、menuType（非必填）、path（非必填）、hideInMenu（非必填）、icon（非必填）、component（非必填）
    updateMenu(menuVos:[DOSMenuVo]):Message
    # 更新单个菜单
    updateSingleMenu(menuVo:DOSSingleMenuVo):Message
    # 新建菜单 - v2.2.10 参数：orderNum（必填）、parentMenuId（必填）、menuType（必填）、name（必填）、path（必填）、hideInMenu（必填）、icon（非必填）、component（非必填）
    saveMenu(menuVo:DOSMenuVo):Message
    #*****************************************************************************************************#

    ########## 指标 相关接口(Mutation)  ##########

    # 添加一个监控指标 -V2.2.8
    addNewMetrics(dosMetricsVo:DOSMetricsVo):Boolean
    # 更新指标信息 - v2.2.8
    updateMetrics(dosMetricsVo:DOSMetricsVo):Boolean
    # 删除指标 - v2.2.8
    deleteMetrics(metricsId:Int):Boolean
    #*****************************************************************************************************#

    ########## 项目版本操作 相关接口(Mutation)  ##########

    # 创建版本 -2.2.0
    creatVersion(operation:DOSVersionOperationVo):DOSProjectVersionPhaseListPo
    # 流程操作 - 2.2.0
    processOperation(operation:DOSVersionOperationVo):DOSProjectVersionPhaseListPo
    # pro部署超过一个小时，点击关闭
    closeProDeploy(versionId:Int):Boolean
    #*****************************************************************************************************#

    ########## 人员操作 相关接口(Mutation)  ##########

    # 添加人员 - v2.2.10
    addBaseUser(userCreateVo:BaseUserCreateVo):String
    # 更新人员信息 - v2.2.10
    updateBaseUser(userUpdateVo:BaseUserUpdateVo):Boolean
    # 删除人员 - v2.2.10
    deleteBaseUser(userId:String!):Boolean
    # 重置密码 - new
    updatePassword(userUpdateVM:DOSUserUpdatePassVM):Boolean
    # 管理员修改用户密码 - new
    changePassword(changePassVM:DOSUserChangePassVM):Boolean
    #*****************************************************************************************************#

    ########## 资源操作 相关接口(Mutation)  ##########

    # 添加资源 - V2.2.12
    saveResource(resource:DOSResource):Boolean
    # 更新资源 - V2.2.12
    updateResource(resource:DOSResource):Boolean
    # 删除资源 - new
    deleteResource(resourceId:Int!):Boolean
    # 绑定资源和项目 - new
    bindResourceProject(resourceId:Int!, projectIds:[Int]!):Boolean
    # 解绑资源和项目 - new
    noBindResourceProject(resourceId:Int!, projectId:Int!):Boolean
    # 普通用户绑定项目和资源信息 - new
    ordinaryBindResourceProject(resourceIds:[Int]!, projectId:Int!, type:Int):Boolean
    #*****************************************************************************************************#

    ########## webHook操作 相关接口(Mutation)  ##########
    # 保存项目绑定webHook
    saveWebhook(webhook:DOSProjectWebhook):Boolean
    # 更新项目绑定webHook
    updateWebhook(webhook:DOSProjectWebhook):Boolean
    # 删除webHook
    deleteWebhook(id:Int):Boolean
    # 保存webhook的配置信息
    saveWebhookCustomInfo(webhookCustomInfo:DOSWebhookCustomInfoVo):Boolean
    # 更新webhook的配置信息
    updateWebhookCustomInfo(webhookCustomInfo:DOSWebhookCustomInfoVo):Boolean
    #*****************************************************************************************************#
    
    ########## kafka topic操作相关接口(Mutation)  ##########
    # 创建kafka topic
    createKafkaTopic(kafkaTopicPo:KafkaTopicPo):Boolean
    # 关联kafka topic和项目
    relateKafkaTopicToProjects(topicId:Int, projectIds:[Int]):Boolean
    # 刷新kafka topic列表
    refreshKafkaTopicList:Boolean
    # 修改kafka topic的描述
    updateKafkaTopicDescription(topicId:Int, description:String):Boolean
    #*****************************************************************************************************#
    
    ########## 导出数据任务 ##########
    # 创建job
    createExportDataJob(exportDataJob:DOSExportDataJob):Boolean
    # 修改job
    updateExportDataJob(exportDataJob:DOSExportDataJob):Boolean
    # 执行job
    executeExportDataJob(jobId:Int!):Boolean
    # 删除job
    deleteExportDataJob(jobId:Int!):Boolean
    # 启动暂停job
    startOrPauseExportDataJob(jobId:Int!, active:Boolean!):Boolean

    # 创建批量job
    createExportDataJobBatch(exportDataJobBatch:DOSExportDataJobBatch):Boolean
    # 修改批量job
    updateExportDataJobBatch(exportDataJobBatch:DOSExportDataJobBatch):Boolean
    # 删除批量job
    deleteExportDataJobBatch(batchId:Int!):Boolean
    # 启动暂停批量job
    startOrPauseExportDataJobBatch(batchId:Int!, active:Boolean!):Boolean
    #*****************************************************************************************************#
    
    ########## 工具管理 ##########
    # 添加工具
    addTool(tool:Tools):Boolean
    # 修改工具
    updateTool(tool:Tools):Boolean
    # 删除工具
    deleteTool(toolId:Int!):Boolean
    # 添加工具分类
    addToolCategory(toolCategory:ToolCategories):Boolean
    # 修改工具分类
    updateToolCategory(toolCategory:ToolCategories):Boolean
    # 删除工具分类
    deleteToolCategory(id:Int!):Boolean
    
    ########## 飞书同步管理 ###########
    # 添加飞书同步数据配置
    saveFeishuBitableSyncConfig(feishuBitableSyncConfig:FeishuBitableSyncConfig):Boolean
    # 修改飞书同步数据配置
    updateFeishuBitableSyncConfig(feishuBitableSyncConfig:FeishuBitableSyncConfig):Boolean
    # 删除飞书同步数据配置
    deleteFeishuBitableSyncConfig(id:Long!):Boolean
    # 启用/停用飞书同步配置
    activeFeishuBitableSyncConfig(id:Long!, active:Boolean!):Boolean
    # 添加飞书app
    addFeishuApp(feishuApp:FeishuApp):Boolean
}
