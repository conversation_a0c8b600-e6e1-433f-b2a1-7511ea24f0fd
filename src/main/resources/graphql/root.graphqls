scalar DateTime
scalar JSON
scalar BigDecimal
scalar Object
scalar Long
schema {
    query: Query
    mutation: Mutation
    subscription: Subscription
}
type Subscription {
    stocks:Stock
    # 订阅版本的审核状态
    monitorVersionStatus(versionId:Int,num:String):Int
    # 订阅版本的结束状态
    monitorVersionEnd(versionId:Int,num:String):Int
    # 订阅pod的运行状态
    monitorPodStatus(podStatusVo:MonitorPodStatusVo):Int
}
type Stock {
    name:String
    price:Long
}
type Query {
                                      ########## 域名解析 相关接口(Query)  ##########

    #获取域名列表
    getDomains(requestVo:DescribeDomainsRequest,pageVo:DOSPageVo):DescribeDomainsResponse
    #获取域名信息
    getDomainInfo(requestVo:DescribeDomainInfoRequest):DescribeDomainInfoResponse
    #获取域名操作日志
    getDomainOperateLogs(requestVo:DescribeDomainLogsRequest,pageVo:DOSPageVo):DescribeDomainLogsResponse
    #获取指定域名的所有的解析记录
    getDomainRecords(request:DescribeDomainRecordsRequest,pageVo:DOSPageVo):DescribeDomainRecordsResponse
    #获取指定解析记录的信息
    getDomainRecordInfo(request:DescribeDomainRecordInfoRequest):DescribeDomainRecordInfoResponse
    #*****************************************************************************************************#

                                      ########## 阿里云日志 相关接口(Query)  ##########

    # 查询阿里云的日志url - v2.2.3
    getLoginUrl(releaseId:Int!):String
    # 重定向查询阿里云的日志url - v2.2.3
    redirectUrl(releaseId:Int!):String
    # 查询yaml文件历史记录 -v2.2.5
    getYamlHistory(kubernetesResourceId:Int):[DOSKubernetesYamlRelation]
    #*****************************************************************************************************#

                                         ########## 日志查询 相关接口(Query)  ##########

    # 查询k8s列表 - v2.2.3  必传参数：projectId，envTypeId
    getKubernetesResources(kubernetesResource:DOSKubernetesResourceVo):[DOSKubernetesResourcePo]
    # 分页查询k8s列表 - V2.2.4
    getPageList(kubernetesResource:DOSKubernetesResourceVo,pageVo:DOSPageVo):DOSKubernetesResourcePoResult
    #*****************************************************************************************************#

                                         ########## 集群查询 相关接口(Query)  ###########

    # 根据集群id获取集群 - new
    getClusterById(clusterId:Int):DOSCluster
    # 获取集群 - 2.2.0
    getClusters(cluster:DOSClusterPage,pageVo:DOSPageVo):DOSClusterResult
    # 获取全部集群 - 2.2.0
    getAllClusters(cluster:DOSClusterPage):[DOSClusterPo]
    # 根据集群id查询集群命名空间 - new
    getClusterNamespaces(namespace:DOSClusterNamespacePage,pageVo:DOSPageVo):DOSClusterNamespaceResult
    # 根据名称获取命名空间 - new
    queryClusterNamespaceByName(namespace:DOSClusterNamespaceVo,pageVo:DOSPageVo):DOSClusterNamespaceResult
    # 根据集群id获取全部的命名空间
    getNamespaces(clusterId:Int!):[DOSClusterNamespace]
    # 根据人员角色信息查询集群和命名空间级联 - v2.2.2
    getClusterNamespace:[DOSClusterNamespacePo]
    # 根据projectId和envType获取集群命名空间二级级联信息 - 2.2.0
    getClustersByProjectIdAndEnvType(projectId:Int!,envType:Int):[DOSClusterNamespacePo]
    # 获取env cluster namespace 三级级联信息 - 2.2.0
    getEnvClusterNamespaceCascade:[DOSEnvClusterPo]
    #*****************************************************************************************************#

                                       ########## 人员查询 相关接口(Query)  ##########

    # 查询所有人员 - new
    getAllUsers:[BaseUser]
    # 查询人员 - new
    getBaseUser:BaseUser
    # 分页查询人员 - v2.2.10
    getPageUsers(dosUserVM:DOSUserVM,inputPage:DOSPageVo):BaseUserTableResult
    # 根据name模糊查询人员 - new
    getUsersByName(name:String):[BaseUser]
    # 生成用户密码
    generateUserPassword:String
    #*****************************************************************************************************#

                                    ########## 项目-项目列表 相关接口(Query)  ##########

    # 查询项目列表 - V2.2.10
    getProjectList(projectVo:DOSProjectVo,pageVo:DOSPageVo):DOSProjectPoResult
    # 获取项目开发人员 - v2.2.10
    getProjectDevelopers(projectId:Int):[DOSProjectUserRelationPo]
    # 获取项目测试人员 - v2.2.10
    getProjectQas(projectId:Int):[DOSProjectUserRelationPo]
    # 查询项目组和环境的列表 - new
    getEnvAndGroup:DOSEnvAndGroup
    # 查询module - new
    getModuleByList(projectId:Int):[DOSProjectModulePo]
    # 查询项目相信信息 - 2.2.0
    getProjectInfo(projectId:Int!):DOSProjectInfoPo
    # 查询已绑定和未绑定资源的项目 - new
    getBindProjects(resourceId:Int!):[DOSBindStatusProject]
    #*****************************************************************************************************#


                                       ########## 项目组 相关接口(Query)  ##########

    # 分页查询项目组 - new
    getProjectGroupList(modelPage:DOSModelVo,pageVo:DOSPageVo):DOSProjectGroupResult
    # 根据项目组名称模糊查询 - new
    getProjectGroupByName(projectGroup:DOSProjectGroupVo,pageVo:DOSPageVo):DOSProjectGroupResult
    # 查询项全部项目组 - new
    getAllProjectGroupList:[DOSProjectGroup]
    # 根据人员角色信息查询项目组和项目的级联 - v2.2.11
    getCascade:[DOSProjectGroupPo]
    # 查询项目成员 - v2.2.10
    getProjectGroupMember(projectGroupId:Int):[DOSProjectGroupUserRelationPo]
    # 查询项目组绑定的技术经理 - v2.2.10
    getProjectGroupManager(projectGroupId:Int):[DOSProjectGroupUserRelationPo]
    # 查询项目组所有人员 - v2.2.10
    getProjectGroupUsers(projectGroupId:Int):[DOSProjectGroupUserRelationPo]
    # 获取待绑定的技术经理列表 - v2.2.10
    getTechnologyManager:[BaseUser]
    #*****************************************************************************************************#





                                          ########## chart列表 相关接口(Query)  ##########
    # 查询chart列表
    getChartList(paramVo:DOSChartParamVo,pageVo:DOSPageVo):DOSChartParamPoResult
    # 查询chart绑定的人员列表
    getChartUserList(chartId:Int):[DOSChartUserVo]
    # 查询部署的chart列表 - new
    getCharts(projectId:Int):[DOSChartPo]
    # 查询环境仓库级连 - new
    getEnvRepositories:[DOSHelmChartRepository]
    # 查询helm的api地址 - new
    getChartUrl:String
    # 查询chart的模板
    getChartTemplates(chartId:Int!,clusterId:Int):[DOSChartTemplate]
    #*****************************************************************************************************#


                                            ########## chart仓库列表 相关接口(Query)  ##########

    # 多条件查询仓库 - new
    getRepositorys(repository:DOSHelmChartRepositoryParam,pageVo:DOSPageVo):DOSHelmChartRepositoryResult
    #*****************************************************************************************************#


                                          ########## 菜单 相关接口(Query)  ##########

    # 根据用户登录信息获取menu列表 - new
    getMenus:[DOSMenuPo]
    # 新菜单查询 -V2.2.5
    getDOSMenus:[DOSMenu]
    # 查询角色绑定和未绑定的菜单 - v2.2.10
    getBindMenus(roleId:Int,isDel:Int):[DOSRoleMenuRelationPo]
    # 获取未添加的菜单 - v2.2.10
    getNoAddMenus:[DOSMenu]
    #*****************************************************************************************************#


                                        ########## 指标 相关接口(Query)  ##########

    # 根据登录人员身份信息分页获取指标信息
    getPageMetrics(dosMetricsVo:DOSMetricsVo,pageVo:DOSPageVo):DOSMetricsPoResult
    # 获取指标相关的所有数据库信息 -V2.2.8
    getAllMetricsDbs:[DOSMetricsDb]
    #*****************************************************************************************************#



                                        ########## k8s release 相关接口(Query)  ##########

    #根据releaseId查询k8s集群内对应的release详情 -V2.2.16
    getK8sReleaseInfo(releaseId:Int):DOSK8sRelease
    # 根据projectId查询k8s集群内对应的项目所有release详情
    getK8sProjectReleaseInfo(releaseVo:DOSReleaseVo):DOSK8sRelease
    #查询release resource详情
    getK8sResourceInfo(release:DOSK8sReleaseVo):DOSK8sRelease
#    # 查询deployment -V2.2.16
#    getK8sDeploymentInfo(deployment:DOSK8sDeploymentVo):[DOSK8sDeployment]
#    # 查询service -V2.2.16
#    getK8sServiceInfo(release:DOSK8sReleaseVo):[DOSK8sService]
#    # 查询pod列表 - new
#    getK8sClusterPod(podVo:DOSPodVo):[DOSPod]
#    # 查询release的ingress - V2.2.16
#    getReleaseIngress(releaseVo:DOSK8sReleaseVo):[DOSK8sIngressPo]
    # 查询pod详情 - new
    getPodInfo(podVo:DOSPodVo):DOSPodInfo
    #*****************************************************************************************************#


                                      ########## 项目版本查询 相关接口(Query)  ##########

    # 获取项目版本操作字典 - 2.2.0
    getOperateAction:[DOSProjectVersionOperateAction]
    # 分页查询版本信息 - 2.2.0
    getVersionPage(operation:DOSVersionOperationVo,pageVo:DOSPageVo):DOSProjectVersionResult
    # 分页查询待审核的项目版本列表 - 2.2.0
    getToBeReviewed(operation:DOSVersionOperationVo,pageVo:DOSPageVo):DOSProjectVersionResult
    # 查询版本详细信息 - 2.2.0
    getVersionInfo(operation:DOSVersionOperationVo):DOSProjectVersionPhaseListPo
    # 查询项目可以选择的环境类型 - 2.2.0
    getSelectEnvType(projectId:Int):[DOSProjectEnvTypePo]
    # 查询版本操作日志 - 2.2.0
    getOperationLog(operationLog:DOSVersionOperationLogVo):[DOSVersionOperationLogPo]
    #*****************************************************************************************************#


                                         ########## release查询 相关接口(Query)  ##########

    # helm查看release的历史 - V2.2.2 必传的参数 releaseId
    historyRelease(param: DOSReleaseParamVo): DOSHelmOperationPo
    # 查询release列表 - V2.2.3 releaseType为必填项
    getReleaseList(releaseVo:DOSReleaseVo,pageVo:DOSPageVo):DOSReleasePoResult
    # 查询values - v2.2.2 必传参数：releaseType=2时releaseId必传；releaseType=1时revision、release、namespace、releaseId必传
    getReleaseValues(valuesVo:DOSReleaseValuesVo):DOSValuePo
    # 根据项目id和环境类型id查询release列表 - 2.2.2 必填参数：envType、projectId、releaseType
    getReleasesByProjectEnvType(releaseVo:DOSReleaseVo):[DOSReleasePo]
    # 查询release的image和tag
    getImageTag(valuesVo:DOSReleaseValuesVo):String

    #*****************************************************************************************************#


                                      ########## 资源查询查询 相关接口(Query)  ##########

    # 分页查询资源 - new
    getResources(resource:DOSResourceVo,pageVo:DOSPageVo):DOSResourceResult
    # 查询已绑定和未绑定项目的资源 - new
    getBindResource(projectId:Int!,type:Int):[DOSBindResourcePo]
    # 查询资源类型 - new
    getAllResourceType:[DOSResourceType]
    # 查询redis列表 - V2.2.12
    getRedisList:[DOSResources]
    # 查询数据库数据源的连接信息 - v2.2.19
    getDatabases(type:Int):[DOSResources]
    #*****************************************************************************************************#
                                    ########## awr报告查询 相关接口(Query)  ##########

    # 分页查询报告
    getAwrReportPage(pageVo:DOSPageVo, timeRangePo:TimeRangePo):DOSOracleAwrReportVo
    # 查询最近几条报告
    getAwrReport(size:Int!):[DOSOracleAwrReport]
    #*****************************************************************************************************#


                                     ########## 用户登录 相关接口(Query)  ##########

    # 返回登录界面 - new
    loginPage:String
    # 用户登录 - new
    login(loginUser:LoginUserVM):BaseUserLoginVm
    # 退出登录 - new
    loginOut:LoginOutVm
    # 通过code登录
    loginByCode(code:String!):BaseUserLoginVm
    #*****************************************************************************************************#


                                      ########## 角色查询 相关接口(Query)  ##########

    # 获取角色列表 - new
    getRoles:[DOSRole]
    # 查询所有的角色信息 - v2.2.10 参数：roleName（非必填）
    getAllRoles(roleVo:DOSRoleVo):[DOSRolePo]
    #*****************************************************************************************************#


                                    ########## 角色查询 相关接口(Query)  ##########
    # 根据登录的人员查询ingress
    getIngressByUser(ingressVo:DOSK8sIngressVo,pageVo:DOSPageVo):DOSK8sIngressResultPo
    # 获取集群下所有的ingress - v2.2.6
    getIngress(clusterVo:DOSClusterVo,pageVo:DOSPageVo):DOSK8sIngressResultPo
    #*****************************************************************************************************#

                                   ########## 项目与webHook关系查询 ##########
    # 跟项目id查询webHook与项目关系列表
    getWebhooks(projectId:Int):[DOSProjectWebhookPo]
    # 分页查询webHook
    getWebhookPage(webhook:DOSProjectWebhook,pageVo:DOSPageVo):DOSProjectWebhookPoResult
    # 测试webhook是否能够连通
    testWebhook(webhook:DOSProjectWebhook):String
    # 测试webhook的配置信息
    testWebhookCustomInfo(webhookInfoVo:DOSWebhookInfoVo):String
    #*****************************************************************************************************#

                                   ########## 查询版本开始结束的release状态 ##########
    # 查询版本创建到结束的release的变化
    getReleaseRecords(releaseRecordVo:DOSProjectVersionReleaseRecordVo):[DOSProjectVersionReleaseRecordListPo]

    ########## 查询kafka topic相关接口 ##########
    # 查询kafka topic列表
    getKafkaTopicList(kafkaTopicPo:KafkaTopicPo,pageVo:DOSPageVo):KafkaTopicVoResult
    # 根据项目id查询kafka topic列表
    getKafkaTopicsByProject(projectId:Int):[KafkaTopicVo]
    # 根据topic id查询topic详情
    getKafkaTopicById(topicId:Int):KafkaTopicVo
    # 根据topic id查询topic的绑定项目列表
    getProjectsBindStatusByKafkaTopicId(topicId:Int):[DOSBindStatusProject]

    ########## 查询export data job相关接口 ##########
    # 查询job详情
    getExportDataJobById(jobId:Int!):DOSExportDataJobPo
    # 查询job列表
    getExportDataJobList(exportDataJob:DOSExportDataJob, pageVo:DOSPageVo):DOSExportDataJobPoResult
    # 查询jobBatch列表
    getExportDataJobBatchList(exportDataJobBatch:DOSExportDataJobBatch, pageVo:DOSPageVo):DOSExportDataJobBatchPoResult
    # 查询job执行记录
    getExportDataJobLogList(exportDataJobLogVo:DOSExportDataJobLogVo, pageVo:DOSPageVo):DOSExportDataJobLogPoResult
    # 根据type查询所有的job
    getAllExportDataJobs(type:Int!):[DOSExportDataJobPo]
    ########## 查询tools相关接口 ##########
    # 查询工具列表
    getTools(tool:Tools, pageVo:DOSPageVo):ToolPageResult
    # 查询工具分类列表
    getToolCategories(pageVo:DOSPageVo):ToolCategoryPageResult
    ########## 查询飞书同步配置相关接口 ##########
    # 查询飞书同步配置列表
    getFeishuBitableSyncConfigList(feishuBitableSyncConfig:FeishuBitableSyncConfig, pageVo:DOSPageVo):FeishuBitableSyncConfigResult
    # 查询飞书同步配置
    getFeishuBitableSyncConfigById(configId:Int!):FeishuBitableSyncConfigVO
    # 查询飞书app列表
    getFeishuAppList:[FeishuAppVO]
}

type Mutation {

                                      ########## 角色操作 相关接口(Mutation)  ##########
    # 为角色分配菜单 - v2.2.10 参数：id（必填），menuIds(必填)
    bindMenus(roleVo:DOSRoleVo):Message
    # 删除角色信息 - v2.2.10
    deleteRole(roleId:Int):Boolean
    # 更新角色信息 - v2.2.10 参数 id（必填），roleName（非必填），roleLevel（非必填），remark（非必填）
    updateRole(dosRole:DOSRoleVo):DOSRole
    # 添加角色 - v2.2.10 参数：roleName（必填），roleLevel（必填），remark（非必填）
    saveRole(dosRole:DOSRoleVo):DOSRole
    #*****************************************************************************************************#


                                         ########## release操作 相关接口(Mutation)  ##########

    # helm部署新release - V2.2.2  必传的参数 projectId,clusterId,namespaceId,chartId,releases,envType;可选参数:values
    deployRelease(param: DOSReleaseParamVo): DOSHelmOperationPo
    # helm升级release - V2.2.2 必传的参数 releaseId 可选参数 values
    upgradeRelease(param: DOSReleaseParamVo): DOSHelmOperationPo
    # helm回滚release - V2.2.2 必传的参数 releaseId,revision
    rollbackRelease(param: DOSReleaseParamVo): DOSHelmOperationPo
    # helm卸载release - V2.2.2 必传的参数 releaseId
    uninstallRelease(param: DOSReleaseParamVo): DOSHelmOperationPo
    # 根据releaseId重启release - 2.2.0
    restartRelease(releaseId:Int!):Boolean
    #*****************************************************************************************************#


                                        ########## 域名解析 相关接口(Mutation)  ##########

    #给指定域名添加解析继续
    addDomainRecord(request:AddDomainRecordRequest):AddDomainRecordResponse
    #删除指定的解析记录
    deleteDomainRecord(request:DeleteDomainRecordRequest):DeleteDomainRecordResponse
    #修改指定的解析记录
    updateDomainRecord(request:UpdateDomainRecordRequest):UpdateDomainRecordResponse
    #*****************************************************************************************************#


                                           ########## kubectl 命令操作 相关接口(Mutation)  ##########

    # yaml文件卸载项目 - v2.2.3
    removeProject(kubernetesResourceId:Int!):Message
    # yaml文件升级项目 - v2.2.3 必传参数：kubernetesResourceId、yamlValues
    upgradeProject(kubernetesResource:DOSKubernetesResourceVo):Message
    # yaml文件部署项目 - v2.2.3 必传的参数：projectId、envTypeId、clusterId、yamlValues、namespaceId
    applyProject(kubernetesResource:DOSKubernetesResourceVo):Message
    #*****************************************************************************************************#


                                         ########## 集群操作 相关接口(Mutation)  ##########

    # 添加集群 - new
    saveCLuster(cluster:DOSClusterVo):DOSClusterPo
    #更新集群信息 - new
    updateCluster(cluster:DOSClusterVo):Boolean
    # 通过集群的id删除集群 - new
    deleteCluster(clusterId:Int!):Boolean
    # 添加集群命名空间 - new
    addClusterNamespace(namespace:DOSClusterNamespaceVo):DOSClusterNamespace
    # 更新集群命名空间 - new
    updateClusterNamespace(namespace:DOSClusterNamespaceVo):Boolean
    #*****************************************************************************************************#


                                          ########## 集群远端操作 相关接口(Mutation)  ##########

    #删除ingress - v2.2.6 必传参数：id、namespaceId、ingressName
    deleteIngress(clusterVo:DOSClusterVo):Message
    #*****************************************************************************************************#


                                      ########## 项目管理-项目组 相关接口(Mutation)  ##########

    # 为项目组分配人员 - v2.2.10
    distributionProjectGroupUsers(relation:DOSProjectGroupUserRelationVo):Boolean
    # 添加项目组 -V2.2.10
    saveProjectGroup(projectGroup:DOSProjectGroupVo):DOSProjectGroup
    # 根据id更新项目组 - new
    updateProjectGroup(projectGroup:DOSProjectGroupVo):Boolean
    # 删除项目组 - new
    deleteProjectGroup(projectGroupId:Int!):Boolean
    #*****************************************************************************************************#


                                       ########## 项目-项目列表 相关接口(Mutation)  ##########

    # 添加信项目 - V2.2.10
    saveProject(project:DOSProjectParam):Boolean
    # 更新项目信息 - V2.2.10
    updateProjects(project:DOSUpdateProjectVo):Boolean
    # 删除项目 - V2.2.10
    deleteProject(projectId:Int!):Boolean
    # 对项目进行授权 -V2.2.10
    authorizeProject(authorizes:[DOSProjectEnvClusterNamespaceRelation]):Boolean
    # 给项目分配开发人员 - V2.2.10
    distributionProjectDevelopers(relation:[DOSProjectUserRelationVo],projectId:Int):Boolean
    # 给项目分配测试人员- V2.2.10
    distributionProjectQas(relation:[DOSProjectUserRelationVo],projectId:Int):Boolean
    # 删除项目分配的人员 -V2.2.10
    deleteProjectUserRelation(relationId:Int):Int
    # 添加module  -V2.2.10
    saveProjectModule(module:DOSProjectModule):DOSProjectModulePo
    # 更新module  -V2.2.10
    updateModule(module:DOSProjectModuleUpdates):Boolean
    # 删除module  -V2.2.10
    deleteModule(moduleId:Int):Boolean
    #*****************************************************************************************************#


                                       ########## k8s release 相关接口(Mutation)  ##########

    # 对deployment扩容 - new
    scaleDeployment(deployment:DOSK8sDeploymentVo):Boolean
    # 重启deployment - new
    restartDeployment(deployment:DOSK8sDeploymentVo):Boolean
    # 删除deployment - new
    deleteDeployment(deployment:DOSK8sDeploymentVo):Boolean
    # 删除pod - new
    deletePod(podVo:DOSPodVo):Boolean
    #*****************************************************************************************************#


                                      ########## chart 相关接口(Mutation)  ##########

    # 添加chart - new
    saveHelm(chart:DOSHelmChartVo):DOSChartParamPo
    # 更新chart - new
    updateChart(chart:DOSHelmChartUpdateVo):Boolean
    # 删除chart - new
    deleteChart(chartId:Int):Boolean
    # 为chart分配人员 - new
    distributionChartUser(relation:DOSHelmChartUserRelationVo):Boolean
    #*****************************************************************************************************#


                                       ########## chart仓库 相关接口(Mutation)  ##########

    # 添加仓库 - new
    addRepository(repository:DOSHelmChartRepositoryVo):DOSHelmChartRepository
    # 根据仓库id更新仓库信息 - new
    updateRepository(repository:DOSHelmChartRepositoryParam):Boolean
    # 根据id删除仓库 - new
    deleteRepository(repositoryId:Int!):Boolean
    #*****************************************************************************************************#


                                       ########## 菜单 相关接口(Mutation)  ##########

    # 删除菜单 - v2.2.10
    deleteMenu(menuId:Int):Message
    # 更新菜单 - v2.2.10 参数：type 1：拖动，2：删除、id(必填)、name（必填）、isDel(必填)、parentMenuId（必填）、orderNum（非必填）、menuType（非必填）、path（非必填）、hideInMenu（非必填）、icon（非必填）、component（非必填）
    updateMenu(menuVos:[DOSMenuVo]):Message
    # 更新单个菜单
    updateSingleMenu(menuVo:DOSSingleMenuVo):Message
    # 新建菜单 - v2.2.10 参数：orderNum（必填）、parentMenuId（必填）、menuType（必填）、name（必填）、path（必填）、hideInMenu（必填）、icon（非必填）、component（非必填）
    saveMenu(menuVo:DOSMenuVo):Message
    #*****************************************************************************************************#


                                        ########## 指标 相关接口(Mutation)  ##########

    # 添加一个监控指标 -V2.2.8
    addNewMetrics(dosMetricsVo:DOSMetricsVo):Boolean
    # 更新指标信息 - v2.2.8
    updateMetrics(dosMetricsVo:DOSMetricsVo):Boolean
    # 删除指标 - v2.2.8
    deleteMetrics(metricsId:Int):Boolean
    #*****************************************************************************************************#



                                         ########## 项目版本操作 相关接口(Mutation)  ##########

    # 创建版本 -2.2.0
    creatVersion(operation:DOSVersionOperationVo):DOSProjectVersionPhaseListPo
    # 流程操作 - 2.2.0
    processOperation(operation:DOSVersionOperationVo):DOSProjectVersionPhaseListPo
    # pro部署超过一个小时，点击关闭
    closeProDeploy(versionId:Int):Boolean
    #*****************************************************************************************************#


                                         ########## 人员操作 相关接口(Mutation)  ##########

    # 添加人员 - v2.2.10
    addBaseUser(userCreateVo:BaseUserCreateVo):String
    # 更新人员信息 - v2.2.10
    updateBaseUser(userUpdateVo:BaseUserUpdateVo):Boolean
    # 删除人员 - v2.2.10
    deleteBaseUser(userId:String!):Boolean
    # 重置密码 - new
    updatePassword(userUpdateVM:DOSUserUpdatePassVM):Boolean
    # 管理员修改用户密码 - new
    changePassword(changePassVM:DOSUserChangePassVM):Boolean
    #*****************************************************************************************************#


                                        ########## 资源操作 相关接口(Mutation)  ##########

    # 添加资源 - V2.2.12
    saveResource(resource:DOSResource):Boolean
    # 更新资源 - V2.2.12
    updateResource(resource:DOSResource):Boolean
    # 删除资源 - new
    deleteResource(resourceId:Int!):Boolean
    # 绑定资源和项目 - new
    bindResourceProject(resourceId:Int!, projectIds:[Int]!):Boolean
    # 解绑资源和项目 - new
    noBindResourceProject(resourceId:Int!, projectId:Int!):Boolean
    # 普通用户绑定项目和资源信息 - new
    ordinaryBindResourceProject(resourceIds:[Int]!, projectId:Int!, type:Int):Boolean
    #*****************************************************************************************************#


                                       ########## 资源操作 相关接口(Mutation)  ##########
    # 保存项目绑定webHook
    saveWebhook(webhook:DOSProjectWebhook):Boolean
    # 更新项目绑定webHook
    updateWebhook(webhook:DOSProjectWebhook):Boolean
    # 删除webHook
    deleteWebhook(id:Int):Boolean
    # 保存webhook的配置信息
    saveWebhookCustomInfo(webhookCustomInfo:DOSWebhookCustomInfoVo):Boolean
    # 更新webhook的配置信息
    updateWebhookCustomInfo(webhookCustomInfo:DOSWebhookCustomInfoVo):Boolean
    #*****************************************************************************************************#
    ########## 资源操作 kafka topic相关接口(Mutation)  ##########
    # 创建kafka topic
    createKafkaTopic(kafkaTopicPo:KafkaTopicPo):Boolean
    # 关联kafka topic和项目
    relateKafkaTopicToProjects(topicId:Int, projectIds:[Int]):Boolean
    # 刷新kafka topic列表
    refreshKafkaTopicList:Boolean
    # 修改kafka topic的描述
    updateKafkaTopicDescription(topicId:Int, description:String):Boolean
    #*****************************************************************************************************#
    ########## 导出数据任务 ##########
    # 创建job
    createExportDataJob(exportDataJob:DOSExportDataJob):Boolean
    # 修改job
    updateExportDataJob(exportDataJob:DOSExportDataJob):Boolean
    # 执行job
    executeExportDataJob(jobId:Int!):Boolean
    # 删除job
    deleteExportDataJob(jobId:Int!):Boolean
    # 启动暂停job
    startOrPauseExportDataJob(jobId:Int!, active:Boolean!):Boolean

    # 创建批量job
    createExportDataJobBatch(exportDataJobBatch:DOSExportDataJobBatch):Boolean
    # 修改批量job
    updateExportDataJobBatch(exportDataJobBatch:DOSExportDataJobBatch):Boolean
    # 删除批量job
    deleteExportDataJobBatch(batchId:Int!):Boolean
    # 启动暂停批量job
    startOrPauseExportDataJobBatch(batchId:Int!, active:Boolean!):Boolean
    #*****************************************************************************************************#
    ########## 工具管理 ##########
    # 添加工具
    addTool(tool:Tools):Boolean
    # 修改工具
    updateTool(tool:Tools):Boolean
    # 删除工具
    deleteTool(toolId:Int!):Boolean
    # 添加工具分类
    addToolCategory(toolCategory:ToolCategories):Boolean
    # 修改工具分类
    updateToolCategory(toolCategory:ToolCategories):Boolean
    # 删除工具分类
    deleteToolCategory(id:Int!):Boolean
    ########## 飞书同步管理 ###########
    # 添加飞书同步数据配置
    saveFeishuBitableSyncConfig(feishuBitableSyncConfig:FeishuBitableSyncConfig):Boolean
    # 添加飞书app
    addFeishuApp(feishuApp:FeishuApp):Boolean
}


type Pagination {
    #当前页码
    pageNum: Int
    #页大小
    pageSize: Int
    #记录总数
    total: Int
}

input DOSPageVo{
    pageNum: Int
    pageSize: Int
}

input PageVo {
    pageNum:Int
    pageSize:Int
}

type Message{
    status:Int
    code:String
    message:String
    data:Object
}

input TimeRangePo {
    startTime: DateTime
    endTime: DateTime
}
