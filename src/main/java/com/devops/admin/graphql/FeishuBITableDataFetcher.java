package com.devops.admin.graphql;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.devops.admin.model.FeishuApp;
import com.devops.admin.model.FeishuBitableSyncConfig;
import com.devops.admin.model.TableResult;
import com.devops.admin.service.FeishuAppService;
import com.devops.admin.service.FeishuBitableSyncConfigService;
import com.devops.admin.util.Assert;
import com.devops.admin.vo.DOSPageVo;
import com.devops.common.annotation.DOSLog;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsMutation;
import com.netflix.graphql.dgs.DgsQuery;
import com.netflix.graphql.dgs.InputArgument;

import javax.annotation.Resource;
import java.util.List;

@DgsComponent
public class FeishuBITableDataFetcher {

    @Resource
    private FeishuBitableSyncConfigService feishuBitableSyncConfigService;

    @Resource
    private FeishuAppService feishuAppService;

    @DgsQuery
    public TableResult<FeishuBitableSyncConfig> getFeishuBitableSyncConfigList(@InputArgument FeishuBitableSyncConfig feishuBitableSyncConfig, @InputArgument DOSPageVo pageVo) {
        return feishuBitableSyncConfigService.listConfig(feishuBitableSyncConfig, pageVo);
    }

    @DgsQuery
    public FeishuBitableSyncConfig getFeishuBitableSyncConfigById(@InputArgument Integer configId) {
        return feishuBitableSyncConfigService.getById(configId);
    }

    @DgsMutation
    @DOSLog(description = "新增飞书同步配置")
    public Boolean saveFeishuBitableSyncConfig(@InputArgument FeishuBitableSyncConfig feishuBitableSyncConfig) {
        return feishuBitableSyncConfigService.save(feishuBitableSyncConfig);
    }

    @DgsMutation
    @DOSLog(description = "修改飞书同步配置")
    public Boolean updateFeishuBitableSyncConfig(@InputArgument FeishuBitableSyncConfig feishuBitableSyncConfig) {
        return feishuBitableSyncConfigService.updateById(feishuBitableSyncConfig);
    }

    @DgsMutation
    @DOSLog(description = "删除飞书同步配置")
    public Boolean deleteFeishuBitableSyncConfig(@InputArgument Long id) {
        FeishuBitableSyncConfig config = feishuBitableSyncConfigService.getById(id);
        Assert.isTrue(!config.getActive(),"请先停用此配置");
        return feishuBitableSyncConfigService.removeById(id);
    }

    @DgsMutation
    @DOSLog(description = "启用/停用飞书同步配置")
    public Boolean activeFeishuBitableSyncConfig(@InputArgument Long id, @InputArgument boolean active) {
        FeishuBitableSyncConfig config = feishuBitableSyncConfigService.getById(id);
        config.setActive(active);
        return feishuBitableSyncConfigService.updateById(config);
    }

    @DgsMutation
    @DOSLog(description = "添加飞书app")
    public Boolean addFeishuApp(@InputArgument FeishuApp feishuApp) {
        Assert.isTrue(feishuAppService.count(Wrappers.<FeishuApp>lambdaQuery().eq(FeishuApp::getAppToken, feishuApp.getAppToken())) == 0, "app_token已存在");
        return feishuAppService.save(feishuApp);
    }

    @DgsQuery
    public List<FeishuApp> getFeishuAppList() {
        return feishuAppService.list();
    }

    @DgsQuery
    public List<FeishuApp> getFeishuAppTableList() {
        return feishuAppService.list();
    }



}
