package com.devops.admin.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.devops.admin.model.FeishuBitableSyncConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【dos_feishu_bitable_sync_config(飞书多维表格同步配置)】的数据库操作Mapper
* @createDate 2025-07-07 14:27:54
* @Entity com.devops.admin.model.FeishuBitableSyncConfig
*/
@Mapper
public interface FeishuBitableSyncConfigMapper extends BaseMapper<FeishuBitableSyncConfig> {
    List<FeishuBitableSyncConfig> listConfig(FeishuBitableSyncConfig feishuBitableSyncConfig, Page<FeishuBitableSyncConfig> page);

    /**
     * 使用 TypeHandler 的 selectById 方法
     */
    FeishuBitableSyncConfig selectByIdWithTypeHandler(Integer id);

}




