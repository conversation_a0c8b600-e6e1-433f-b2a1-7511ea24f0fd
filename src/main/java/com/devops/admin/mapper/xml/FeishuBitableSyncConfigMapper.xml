<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.devops.admin.mapper.FeishuBitableSyncConfigMapper">

    <resultMap id="BaseResultMap" type="com.devops.admin.model.FeishuBitableSyncConfig">
            <id property="id" column="id" />
            <result property="name" column="name" />
            <result property="appId" column="app_id" />
            <result property="tableId" column="table_id" />
            <result property="tableName" column="table_name" />
            <result property="dataApiUrl" column="data_api_url" />
            <result property="primaryKeyList" column="primary_key_list" jdbcType="ARRAY" typeHandler="com.devops.admin.config.StringListTypeHandler"/>
            <result property="fieldsConfigList" column="fields_config_list" jdbcType="OTHER" typeHandler="com.devops.admin.config.JSONTypeHandlerPg"/>
            <result property="cronExpression" column="cron_expression" />
            <result property="active" column="active" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="deletedAt" column="deleted_at" />
    </resultMap>

    <sql id="Base_Column_List">
        id,name,app_id,table_id,table_name,data_api_url,
        primary_key_list,fields_config_list,cron_expression,active,created_at,
        updated_at,deleted_at
    </sql>

    <select id="listConfig" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from dos_feishu_bitable_sync_config
        where deleted_at is null
        <if test="feishuBitableSyncConfig.name != null and feishuBitableSyncConfig.name != ''">
            and name like CONCAT(CONCAT('%', #{feishuBitableSyncConfig.name}), '%')
        </if>
        order by created_at desc
    </select>

    <!-- 添加自定义的 selectById 方法，使用 BaseResultMap -->
    <select id="selectByIdWithTypeHandler" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from dos_feishu_bitable_sync_config
        where id = #{id} and deleted_at is null
    </select>

</mapper>
