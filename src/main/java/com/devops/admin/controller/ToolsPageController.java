package com.devops.admin.controller;

import cn.hutool.json.JSONObject;
import com.devops.admin.model.FeishuBitableSyncConfig;
import com.devops.admin.service.FeishuBitableSyncConfigService;
import com.devops.admin.service.ToolCategoriesService;
import com.devops.admin.service.ToolsService;
import com.devops.common.utils.Message;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Notes
 * @date 2024/3/8 12:01
 */
@Slf4j
@RestController
@RequestMapping("/tools")
@Api(value = "/tools",tags = "工具页相关接口")
public class ToolsPageController {
    @Resource
    private ToolCategoriesService toolCategoriesService;
    @GetMapping(value = "/all")
    @ApiOperation(value="所有工具",notes = "所有工具")
    public Message allTools() {
        return new Message(toolCategoriesService.queryAllToolsMap());
    }

    @Resource
    private FeishuBitableSyncConfigService feishuBitableSyncConfigService;

    @GetMapping(value = "/config/{id}")
    public FeishuBitableSyncConfig getConfigById(@PathVariable("id") Integer id) {
        return feishuBitableSyncConfigService.getById(id);
    }
}
