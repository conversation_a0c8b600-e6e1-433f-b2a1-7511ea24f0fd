package com.devops.admin.config;

import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

@MappedJdbcTypes(JdbcType.ARRAY)
@MappedTypes(List.class)
public class StringListTypeHandler extends BaseTypeHandler<List<String>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType)
            throws SQLException {
        Connection conn = ps.getConnection();
        Array array = conn.createArrayOf("text", parameter.toArray());
        ps.setArray(i, array);
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        System.out.println("StringListTypeHandler.getNullableResult called for column: " + columnName);
        Array array = rs.getArray(columnName);
        System.out.println("Array value: " + array);
        if (array != null) {
            Object arrayData = array.getArray();
            System.out.println("Array data: " + arrayData + ", type: " + (arrayData != null ? arrayData.getClass() : "null"));
            if (arrayData instanceof String[]) {
                List<String> result = Arrays.asList((String[]) arrayData);
                System.out.println("Converted result: " + result);
                return result;
            }
        }
        System.out.println("Returning null");
        return null;
    }

    @Override
    public List<String> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        System.out.println("StringListTypeHandler.getNullableResult called for columnIndex: " + columnIndex);
        Array array = rs.getArray(columnIndex);
        System.out.println("Array value: " + array);
        if (array != null) {
            Object arrayData = array.getArray();
            System.out.println("Array data: " + arrayData + ", type: " + (arrayData != null ? arrayData.getClass() : "null"));
            if (arrayData instanceof String[]) {
                List<String> result = Arrays.asList((String[]) arrayData);
                System.out.println("Converted result: " + result);
                return result;
            }
        }
        System.out.println("Returning null");
        return null;
    }

    @Override
    public List<String> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        System.out.println("StringListTypeHandler.getNullableResult called for CallableStatement columnIndex: " + columnIndex);
        Array array = cs.getArray(columnIndex);
        System.out.println("Array value: " + array);
        if (array != null) {
            Object arrayData = array.getArray();
            System.out.println("Array data: " + arrayData + ", type: " + (arrayData != null ? arrayData.getClass() : "null"));
            if (arrayData instanceof String[]) {
                List<String> result = Arrays.asList((String[]) arrayData);
                System.out.println("Converted result: " + result);
                return result;
            }
        }
        System.out.println("Returning null");
        return null;
    }
}