package com.devops.admin.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.devops.admin.config.JSONTypeHandlerPg;
import com.devops.admin.config.StringListTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * 飞书多维表格同步配置
 * @TableName dos_feishu_bitable_sync_config
 */
@TableName(value ="dos_feishu_bitable_sync_config")
@Data
public class FeishuBitableSyncConfig extends BaseModel {
    /**
     * 配置名称
     */
    private String name;

    /**
     * 飞书应用id
     */
    private Integer appId;

    /**
     * 飞书多维表格id
     */
    private String tableId;

    /**
     * 飞书多维表格数据表名称
     */
    private String tableName;

    /**
     * 数据接口地址
     */
    private String dataApiUrl;

    /**
     * 飞书数据表主键列表
     */
    @TableField(jdbcType = JdbcType.ARRAY, typeHandler = StringListTypeHandler.class)
    private List<String> primaryKeyList;

    /**
     * 字段配置
     */
    @TableField(typeHandler = JSONTypeHandlerPg.class)
    private Object fieldsConfigList;

    /**
     * cron表达式
     */
    private String cronExpression;

    /**
     * 是否启用
     */
    private Boolean active;
}