/**
 * Author:yuiop
 * Date:2022/10/20 16:30
 */
package com.devops.admin.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.devops.admin.model.DOSKubernetesResource;
import com.devops.admin.model.DOSRelease;
import com.devops.admin.vo.DOSK8sReleaseVo;
import com.devops.admin.vo.DOSNamespaceChart;

import java.util.List;

public interface DOSReleaseOutService {
    DOSNamespaceChart dealDbRelease(DOSK8sReleaseVo release);
    DOSNamespaceChart getInfoByReleaseId(int releaseId);
    DOSNamespaceChart getInfoByResourceId(int resourceId);

    List<DOSRelease> selectListByReleaseName(String releaseName);

    List<DOSKubernetesResource> getK8sList(LambdaQueryWrapper<DOSKubernetesResource> kubernetesResourceName);

    DOSRelease getReleseById(Integer releaseId);

    DOSRelease getReleaseByReleaseName(String releaseName);

    DOSKubernetesResource getKubernetesResourceByResourceName(String resourceName);
}
