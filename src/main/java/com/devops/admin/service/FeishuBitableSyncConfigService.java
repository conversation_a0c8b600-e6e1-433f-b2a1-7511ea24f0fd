package com.devops.admin.service;

import com.devops.admin.model.FeishuBitableSyncConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.devops.admin.model.TableResult;
import com.devops.admin.vo.DOSPageVo;

/**
* <AUTHOR>
* @description 针对表【dos_feishu_bitable_sync_config(飞书多维表格同步配置)】的数据库操作Service
* @createDate 2025-07-07 14:27:54
*/
public interface FeishuBitableSyncConfigService extends IService<FeishuBitableSyncConfig> {

    TableResult<FeishuBitableSyncConfig> listConfig(FeishuBitableSyncConfig feishuBitableSyncConfig, DOSPageVo pageVo);

}
