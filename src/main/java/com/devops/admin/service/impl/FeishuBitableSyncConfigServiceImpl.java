package com.devops.admin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.devops.admin.model.FeishuBitableSyncConfig;
import com.devops.admin.model.Pagination;
import com.devops.admin.model.TableResult;
import com.devops.admin.service.FeishuBitableSyncConfigService;
import com.devops.admin.mapper.FeishuBitableSyncConfigMapper;
import com.devops.admin.vo.DOSPageVo;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【dos_feishu_bitable_sync_config(飞书多维表格同步配置)】的数据库操作Service实现
* @createDate 2025-07-07 14:27:54
*/
@Service
public class FeishuBitableSyncConfigServiceImpl extends ServiceImpl<FeishuBitableSyncConfigMapper, FeishuBitableSyncConfig>
    implements FeishuBitableSyncConfigService{

    @Override
    public TableResult<FeishuBitableSyncConfig> listConfig(FeishuBitableSyncConfig feishuBitableSyncConfig, DOSPageVo pageVo) {
        Page<FeishuBitableSyncConfig> page = new Page<>(pageVo.getPageNum(), pageVo.getPageSize());
        List<FeishuBitableSyncConfig> configList = baseMapper.listConfig(feishuBitableSyncConfig, page);
        Pagination p = Pagination.builder().pageNum(page.getCurrent()).pageSize(page.getSize()).total(page.getTotal()).build();
        return new TableResult<>(configList, p);
    }

    /**
     * 重写 getById 方法，使用带 TypeHandler 的查询
     */
    @Override
    public FeishuBitableSyncConfig getById(Serializable id) {
        return baseMapper.selectByIdWithTypeHandler((Integer) id);
    }
}




