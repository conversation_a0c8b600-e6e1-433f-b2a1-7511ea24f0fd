package com.devops.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.lark.oapi.service.bitable.v1.model.AppTable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class FeishuClientService {

    @Value("${feishu.adapter.service.url:http://localhost:8080}")
    private String feishuAdapterServiceUrl;

    public List<AppTable> getTables(String appToken) {
        HttpResponse response = HttpUtil.createGet(StrUtil.format("{}/{}/tables", feishuAdapterServiceUrl, appToken))
                .timeout(10000)
                .execute();
        if (response.isOk()) {
            return JSONUtil.toList(response.body(), AppTable.class);
        } else {
            log.error("response code {}, feishu adapter service error, appToken:{}", response.getStatus(), appToken);
        }
        return null;
    }
}
