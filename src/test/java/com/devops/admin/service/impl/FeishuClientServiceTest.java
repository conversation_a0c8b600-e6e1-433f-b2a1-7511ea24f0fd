package com.devops.admin.service.impl;

import com.devops.admin.config.TestConfig;
import com.devops.admin.service.FeishuClientService;
import com.lark.oapi.service.bitable.v1.model.AppTable;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import javax.annotation.Resource;
import java.util.List;

/**
 * 飞书客户端服务测试类
 *
 * <AUTHOR> Team
 */
@SpringBootTest(
    classes = {FeishuClientService.class},
    webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@Import(TestConfig.class)
@ActiveProfiles("test")
class FeishuClientServiceTest {

    @Resource
    private FeishuClientService feishuClientService;
    

    /**
     * 测试获取数据表列表 - 同步方式（用于快速验证）
     */
    @Test
    void getTablesSync() {
        String testAppToken = "L50gboVtwaQIYAsBOEZc6i0nnpc";

        try {
            // 同步获取结果（仅用于测试）
            List<AppTable> tableList = feishuClientService.getTables(testAppToken);

            System.out.println("同步获取结果:");
            if (tableList != null) {
                System.out.println("数据表数量: " + tableList.size());
                tableList.forEach(table -> {
                    System.out.println("- 表ID: " + table.getTableId() + ", 表名: " + table.getName());
                });
            } else {
                System.out.println("未获取到数据表列表");
            }
        } catch (Exception e) {
            System.err.println("测试执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}