package com.devops.admin.service;

import com.devops.admin.model.FeishuTablesResponse;
import com.lark.oapi.service.bitable.v1.model.AppTable;
import org.junit.jupiter.api.Test;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 简单的飞书客户端测试
 * 不依赖Spring上下文，直接测试核心逻辑
 * 
 * <AUTHOR> Team
 */
public class SimpleFeishuClientTest {

    /**
     * 测试响应解析逻辑
     */
    @Test
    public void testResponseParsing() {
        System.out.println("=== 测试飞书API响应解析逻辑 ===");
        
        // 创建测试响应对象
        FeishuTablesResponse response = new FeishuTablesResponse();
        response.setCode(0);
        response.setMsg("success");
        
        FeishuTablesResponse.Data data = new FeishuTablesResponse.Data();
        data.setTotal(2);
        data.setHasMore(false);
        data.setPageToken("next_page_token");
        
        // 创建模拟的AppTable对象
        AppTable table1 = new AppTable();
        table1.setTableId("tbl123456");
        table1.setName("测试数据表1");
        
        AppTable table2 = new AppTable();
        table2.setTableId("tbl789012");
        table2.setName("测试数据表2");
        
        data.setItems(Arrays.asList(table1, table2));
        response.setData(data);
        
        // 验证响应结构
        assert response.getCode() == 0 : "响应码应该为0";
        assert "success".equals(response.getMsg()) : "响应消息应该为success";
        assert response.getData() != null : "响应数据不应该为null";
        assert response.getData().getItems() != null : "数据表列表不应该为null";
        assert response.getData().getItems().size() == 2 : "数据表数量应该为2";
        assert "tbl123456".equals(response.getData().getItems().get(0).getTableId()) : "第一个表ID不匹配";
        assert "测试数据表1".equals(response.getData().getItems().get(0).getName()) : "第一个表名不匹配";
        
        System.out.println("✅ 响应解析测试通过");
        System.out.println("响应码: " + response.getCode());
        System.out.println("响应消息: " + response.getMsg());
        System.out.println("数据表数量: " + response.getData().getItems().size());
        
        response.getData().getItems().forEach(table -> {
            System.out.println("- 表ID: " + table.getTableId() + ", 表名: " + table.getName());
        });
    }

    /**
     * 测试转换逻辑
     */
    @Test
    public void testConversionLogic() {
        System.out.println("\n=== 测试List<AppTable>转换逻辑 ===");
        
        // 模拟成功响应的转换
        FeishuTablesResponse successResponse = createSuccessResponse();
        List<AppTable> successResult = convertResponse(successResponse);
        
        assert successResult != null : "成功响应转换结果不应该为null";
        assert successResult.size() == 2 : "成功响应应该包含2个数据表";
        System.out.println("✅ 成功响应转换测试通过，数据表数量: " + successResult.size());
        
        // 模拟错误响应的转换
        FeishuTablesResponse errorResponse = createErrorResponse();
        List<AppTable> errorResult = convertResponse(errorResponse);
        
        assert errorResult != null : "错误响应转换结果不应该为null";
        assert errorResult.isEmpty() : "错误响应应该返回空列表";
        System.out.println("✅ 错误响应转换测试通过，返回空列表");
        
        // 模拟null响应的转换
        List<AppTable> nullResult = convertResponse(null);
        assert nullResult != null : "null响应转换结果不应该为null";
        assert nullResult.isEmpty() : "null响应应该返回空列表";
        System.out.println("✅ null响应转换测试通过，返回空列表");
    }

    /**
     * 测试响应式流处理
     */
    @Test
    public void testReactiveFlow() {
        System.out.println("\n=== 测试响应式流处理 ===");
        
        // 创建成功响应的Mono
        Mono<FeishuTablesResponse> successMono = Mono.just(createSuccessResponse());
        Mono<List<AppTable>> successResult = successMono.map(this::convertResponse);
        
        StepVerifier.create(successResult)
                .expectNextMatches(tables -> {
                    System.out.println("响应式流测试 - 获取到数据表数量: " + tables.size());
                    tables.forEach(table -> {
                        System.out.println("- 表ID: " + table.getTableId() + ", 表名: " + table.getName());
                    });
                    return tables.size() == 2;
                })
                .verifyComplete();
        
        System.out.println("✅ 响应式流处理测试通过");
        
        // 测试错误处理
        Mono<FeishuTablesResponse> errorMono = Mono.error(new RuntimeException("网络错误"));
        Mono<List<AppTable>> errorResult = errorMono
                .map(this::convertResponse)
                .onErrorReturn(Collections.emptyList());
        
        StepVerifier.create(errorResult)
                .expectNextMatches(tables -> {
                    System.out.println("错误处理测试 - 返回空列表，大小: " + tables.size());
                    return tables.isEmpty();
                })
                .verifyComplete();
        
        System.out.println("✅ 错误处理测试通过");
    }

    /**
     * 创建成功响应
     */
    private FeishuTablesResponse createSuccessResponse() {
        FeishuTablesResponse response = new FeishuTablesResponse();
        response.setCode(0);
        response.setMsg("success");
        
        FeishuTablesResponse.Data data = new FeishuTablesResponse.Data();
        data.setTotal(2);
        data.setHasMore(false);
        
        AppTable table1 = new AppTable();
        table1.setTableId("tbl_success_1");
        table1.setName("成功测试表1");
        
        AppTable table2 = new AppTable();
        table2.setTableId("tbl_success_2");
        table2.setName("成功测试表2");
        
        data.setItems(Arrays.asList(table1, table2));
        response.setData(data);
        
        return response;
    }

    /**
     * 创建错误响应
     */
    private FeishuTablesResponse createErrorResponse() {
        FeishuTablesResponse response = new FeishuTablesResponse();
        response.setCode(40001);
        response.setMsg("Invalid app token");
        return response;
    }

    /**
     * 转换响应为List<AppTable>
     * 这是FeishuClientService中getTables方法的核心逻辑
     */
    private List<AppTable> convertResponse(FeishuTablesResponse response) {
        if (response != null && response.getCode() == 0 && response.getData() != null) {
            List<AppTable> tables = response.getData().getItems();
            return tables != null ? tables : Collections.emptyList();
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 演示如何使用转换后的结果
     */
    @Test
    public void testUsageExample() {
        System.out.println("\n=== 使用示例 ===");
        
        // 模拟获取数据表列表
        List<AppTable> tables = convertResponse(createSuccessResponse());
        
        System.out.println("获取到 " + tables.size() + " 个数据表:");
        
        // 遍历数据表
        for (int i = 0; i < tables.size(); i++) {
            AppTable table = tables.get(i);
            System.out.println((i + 1) + ". 表ID: " + table.getTableId() + ", 表名: " + table.getName());
        }
        
        // 查找特定表
        String targetTableName = "成功测试表1";
        AppTable foundTable = tables.stream()
                .filter(table -> targetTableName.equals(table.getName()))
                .findFirst()
                .orElse(null);
        
        if (foundTable != null) {
            System.out.println("✅ 找到目标表: " + foundTable.getName() + " (ID: " + foundTable.getTableId() + ")");
        } else {
            System.out.println("❌ 未找到目标表: " + targetTableName);
        }
        
        // 检查表是否存在
        String targetTableId = "tbl_success_1";
        boolean exists = tables.stream()
                .anyMatch(table -> targetTableId.equals(table.getTableId()));
        
        System.out.println("表 " + targetTableId + " 是否存在: " + (exists ? "✅ 是" : "❌ 否"));
    }
}
