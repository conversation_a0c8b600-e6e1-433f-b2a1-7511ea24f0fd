package com.devops.admin.service;

import com.devops.admin.model.FeishuTablesResponse;
import com.lark.oapi.service.bitable.v1.model.AppTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 飞书客户端服务单元测试
 * 不依赖Spring上下文，使用Mock进行测试
 * 
 * <AUTHOR> Team
 */
@ExtendWith(MockitoExtension.class)
class FeishuClientServiceUnitTest {

    @Mock
    private WebClient mockWebClient;

    @Mock
    private WebClient.RequestHeadersUriSpec mockRequestHeadersUriSpec;

    @Mock
    private WebClient.RequestHeadersSpec mockRequestHeadersSpec;

    @Mock
    private WebClient.ResponseSpec mockResponseSpec;

    private FeishuClientService feishuClientService;

    @BeforeEach
    void setUp() throws Exception {
        feishuClientService = new FeishuClientService();
        
        // 使用反射设置私有字段
        Field webClientField = FeishuClientService.class.getDeclaredField("feishuWebClient");
        webClientField.setAccessible(true);
        webClientField.set(feishuClientService, mockWebClient);
    }

    /**
     * 测试成功获取数据表列表
     */
    @Test
    void testGetTablesSuccess() {
        // 准备测试数据
        AppTable mockTable1 = new AppTable();
        mockTable1.setTableId("tbl123");
        mockTable1.setName("测试表1");
        
        AppTable mockTable2 = new AppTable();
        mockTable2.setTableId("tbl456");
        mockTable2.setName("测试表2");
        
        List<AppTable> mockTables = Arrays.asList(mockTable1, mockTable2);
        
        FeishuTablesResponse mockResponse = new FeishuTablesResponse();
        mockResponse.setCode(0);
        mockResponse.setMsg("success");
        
        FeishuTablesResponse.Data mockData = new FeishuTablesResponse.Data();
        mockData.setItems(mockTables);
        mockData.setTotal(2);
        mockData.setHasMore(false);
        mockResponse.setData(mockData);

        // 设置Mock行为
        when(mockWebClient.get()).thenReturn(mockRequestHeadersUriSpec);
        when(mockRequestHeadersUriSpec.uri(anyString())).thenReturn(mockRequestHeadersSpec);
        when(mockRequestHeadersSpec.retrieve()).thenReturn(mockResponseSpec);
        when(mockResponseSpec.bodyToMono(FeishuTablesResponse.class)).thenReturn(Mono.just(mockResponse));

        // 执行测试
        String testAppToken = "test_app_token";
        Mono<List<AppTable>> result = feishuClientService.getTables(testAppToken);

        // 验证结果
        StepVerifier.create(result)
                .expectNextMatches(tables -> {
                    System.out.println("测试成功 - 获取到的数据表数量: " + tables.size());
                    tables.forEach(table -> {
                        System.out.println("- 表ID: " + table.getTableId() + ", 表名: " + table.getName());
                    });
                    return tables.size() == 2 && 
                           "tbl123".equals(tables.get(0).getTableId()) &&
                           "测试表1".equals(tables.get(0).getName());
                })
                .verifyComplete();
    }

    /**
     * 测试API返回错误码的情况
     */
    @Test
    void testGetTablesWithErrorCode() {
        // 准备错误响应数据
        FeishuTablesResponse mockResponse = new FeishuTablesResponse();
        mockResponse.setCode(40001);
        mockResponse.setMsg("Invalid app token");

        // 设置Mock行为
        when(mockWebClient.get()).thenReturn(mockRequestHeadersUriSpec);
        when(mockRequestHeadersUriSpec.uri(anyString())).thenReturn(mockRequestHeadersSpec);
        when(mockRequestHeadersSpec.retrieve()).thenReturn(mockResponseSpec);
        when(mockResponseSpec.bodyToMono(FeishuTablesResponse.class)).thenReturn(Mono.just(mockResponse));

        // 执行测试
        String testAppToken = "invalid_token";
        Mono<List<AppTable>> result = feishuClientService.getTables(testAppToken);

        // 验证结果 - 应该返回空列表
        StepVerifier.create(result)
                .expectNextMatches(tables -> {
                    System.out.println("错误码测试 - 返回空列表，大小: " + tables.size());
                    return tables.isEmpty();
                })
                .verifyComplete();
    }

    /**
     * 测试网络异常的情况
     */
    @Test
    void testGetTablesWithNetworkError() {
        // 设置Mock行为 - 模拟网络异常
        when(mockWebClient.get()).thenReturn(mockRequestHeadersUriSpec);
        when(mockRequestHeadersUriSpec.uri(anyString())).thenReturn(mockRequestHeadersSpec);
        when(mockRequestHeadersSpec.retrieve()).thenReturn(mockResponseSpec);
        when(mockResponseSpec.bodyToMono(FeishuTablesResponse.class))
                .thenReturn(Mono.error(new RuntimeException("Network error")));

        // 执行测试
        String testAppToken = "test_token";
        Mono<List<AppTable>> result = feishuClientService.getTables(testAppToken);

        // 验证结果 - 应该返回空列表（因为有错误处理）
        StepVerifier.create(result)
                .expectNextMatches(tables -> {
                    System.out.println("网络异常测试 - 返回空列表，大小: " + tables.size());
                    return tables.isEmpty();
                })
                .verifyComplete();
    }

    /**
     * 测试响应数据为null的情况
     */
    @Test
    void testGetTablesWithNullResponse() {
        // 设置Mock行为 - 返回null
        when(mockWebClient.get()).thenReturn(mockRequestHeadersUriSpec);
        when(mockRequestHeadersUriSpec.uri(anyString())).thenReturn(mockRequestHeadersSpec);
        when(mockRequestHeadersSpec.retrieve()).thenReturn(mockResponseSpec);
        when(mockResponseSpec.bodyToMono(FeishuTablesResponse.class)).thenReturn(Mono.just((FeishuTablesResponse) null));

        // 执行测试
        String testAppToken = "test_token";
        Mono<List<AppTable>> result = feishuClientService.getTables(testAppToken);

        // 验证结果
        StepVerifier.create(result)
                .expectNextMatches(tables -> {
                    System.out.println("空响应测试 - 返回空列表，大小: " + tables.size());
                    return tables.isEmpty();
                })
                .verifyComplete();
    }

    /**
     * 测试响应解析逻辑
     */
    @Test
    void testResponseParsing() {
        // 创建测试响应对象
        FeishuTablesResponse response = new FeishuTablesResponse();
        response.setCode(0);
        response.setMsg("success");
        
        FeishuTablesResponse.Data data = new FeishuTablesResponse.Data();
        data.setTotal(1);
        data.setHasMore(false);
        data.setPageToken("next_page_token");
        
        AppTable table = new AppTable();
        table.setTableId("test_table_id");
        table.setName("测试数据表");
        data.setItems(Collections.singletonList(table));
        
        response.setData(data);
        
        // 验证响应结构
        assert response.getCode() == 0;
        assert "success".equals(response.getMsg());
        assert response.getData() != null;
        assert response.getData().getItems() != null;
        assert response.getData().getItems().size() == 1;
        assert "test_table_id".equals(response.getData().getItems().get(0).getTableId());
        
        System.out.println("响应解析测试通过");
    }
}
