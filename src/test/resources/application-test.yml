# 测试环境配置
spring:
  profiles:
    active: test
  
  # 数据库配置 - 使用内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
  
  # 禁用Web相关的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration
      - org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration
      - org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration
      - org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration

# 日志配置
logging:
  level:
    com.devops.admin: DEBUG
    org.springframework.web.reactive.function.client: DEBUG
    reactor.netty.http.client: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 测试用的飞书配置
feishu:
  base-url: https://open.feishu.cn/open-apis/bitable/v1/apps
  timeout: 30000
